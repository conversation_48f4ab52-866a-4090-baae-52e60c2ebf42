# ===================================================================
# Spring Boot configuration.
#
# This configuration will be overridden by the Spring profile you use,
# for example application-dev.yml if you use the "dev" profile.
#
# More information on profiles: https://www.jhipster.tech/profiles/
# More information on configuration properties: https://www.jhipster.tech/common-application-properties/
# ===================================================================

# ===================================================================
# Standard Spring Boot properties.
# Full reference is available at:
# http://docs.spring.io/spring-boot/docs/current/reference/html/common-application-properties.html
# ===================================================================

---
# Conditionally disable springdoc on missing api-docs profile
spring:
  config:
    activate:
      on-profile: '!api-docs'
springdoc:
  api-docs:
    enabled: false
---
feign:
  circuitbreaker:
    enabled: true
  # client:
  #   config:
  #     default:
  #       connectTimeout: 5000
  #       readTimeout: 5000
management:
  endpoints:
    web:
      base-path: /management
      exposure:
        include:
          - configprops
          - env
          - health
          - info
          - jhimetrics
          - jhiopenapigroups
          - logfile
          - loggers
          - prometheus
          - threaddump
          - caches
          - liquibase
  endpoint:
    health:
      show-details: when_authorized
      roles: 'ROLE_ADMIN'
      probes:
        enabled: true
      group:
        liveness:
          include: livenessState
        readiness:
          include: readinessState,db
    jhimetrics:
      enabled: true
  info:
    git:
      mode: full
    env:
      enabled: true
  health:
    mail:
      enabled: false # When using the MailService, configure an SMTP server and set this to true
  prometheus:
    metrics:
      export:
        enabled: true
        step: 60
  observations:
    key-values:
      application: ${spring.application.name}
  metrics:
    enable:
      http: true
      jvm: true
      logback: true
      process: true
      system: true
    distribution:
      percentiles-histogram:
        all: true
      percentiles:
        all: 0, 0.5, 0.75, 0.95, 0.99, 1.0
    data:
      repository:
        autotime:
          enabled: true
    tags:
      application: ${spring.application.name}

spring:
  application:
    name: whiskerguardGeneralService
  cloud:
    consul:
      discovery:
        healthCheckPath: /management/health
        instanceId: whiskerguardgeneralservice:${spring.application.instance-id:${random.value}}
        service-name: whiskerguardgeneralservice
        healthCheckInterval: 10s
        healthCheckTimeout: 5s
        healthCheckCriticalTimeout: 30s
        deregister: true
      config:
        watch:
          enabled: false
  docker:
    compose:
      enabled: true
      lifecycle-management: start-only
      file: src/main/docker/mysql.yml
  profiles:
    # The commented value for `active` can be replaced with valid Spring profiles to load.
    # Otherwise, it will be filled in by maven when building the JAR file
    # Either way, it can be overridden by `--spring.profiles.active` value passed in the commandline or `-Dspring.profiles.active` set in `JAVA_OPTS`
    active: '@spring.profiles.active@'
    group:
      dev:
        - dev
        - api-docs
        # Uncomment to activate TLS for the dev profile
        #- tls
  jmx:
    enabled: false
  data:
    jpa:
      repositories:
        bootstrap-mode: deferred
  jpa:
    open-in-view: false
    properties:
      hibernate.jdbc.time_zone: UTC
      hibernate.timezone.default_storage: NORMALIZE
      hibernate.type.preferred_instant_jdbc_type: TIMESTAMP
      hibernate.id.new_generator_mappings: true
      hibernate.connection.provider_disables_autocommit: true
      hibernate.cache.use_second_level_cache: false
      hibernate.cache.use_query_cache: false
      hibernate.generate_statistics: false
      # modify batch size as necessary
      hibernate.jdbc.batch_size: 25
      hibernate.order_inserts: true
      hibernate.order_updates: true
      hibernate.query.fail_on_pagination_over_collection_fetch: true
      hibernate.query.in_clause_parameter_padding: true
    hibernate:
      ddl-auto: none
      naming:
        physical-strategy: org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy
        implicit-strategy: org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy
  messages:
    basename: i18n/messages
  main:
    allow-bean-definition-overriding: true
  mvc:
    problemdetails:
      enabled: true
  security:
    oauth2:
      resourceserver:
        jwt:
          authority-prefix: ''
          authorities-claim-name: auth
  task:
    execution:
      thread-name-prefix: whiskerguard-general-service-task-
      pool:
        core-size: 2
        max-size: 50
        queue-capacity: 10000
    scheduling:
      thread-name-prefix: whiskerguard-general-service-scheduling-
      pool:
        size: 2
  thymeleaf:
    mode: HTML
  output:
    ansi:
      console-available: true

server:
  servlet:
    session:
      cookie:
        http-only: true

springdoc:
  show-actuator: true

# Properties to be exposed on the /info management endpoint
info:
  # Comma separated list of profiles that will trigger the ribbon to show
  display-ribbon-on-profiles: 'dev'

# ===================================================================
# JHipster specific properties
#
# Full reference is available at: https://www.jhipster.tech/common-application-properties/
# ===================================================================

jhipster:
  clientApp:
    name: 'whiskerguardGeneralServiceApp'
  # By default CORS is disabled. Uncomment to enable.
  # cors:
  #   allowed-origins: "http://localhost:8100,http://localhost:9000"
  #   allowed-methods: "*"
  #   allowed-headers: "*"
  #   exposed-headers: "Authorization,Link,X-Total-Count,X-${jhipster.clientApp.name}-alert,X-${jhipster.clientApp.name}-error,X-${jhipster.clientApp.name}-params"
  #   allow-credentials: true
  #   max-age: 1800
  mail:
    from: whiskerguardGeneralService@localhost
  api-docs:
    default-include-pattern: /api/**
    management-include-pattern: /management/**
    title: Whiskerguard General Service API
    description: Whiskerguard General Service API documentation
    version: 0.0.1
    terms-of-service-url:
    contact-name:
    contact-url:
    contact-email:
    license: unlicensed
    license-url:
  security:

# jhipster-needle-add-application-yaml-document
---
# ===================================================================
# Application specific properties
# Add your own application properties here, see the ApplicationProperties class
# to have type-safe configuration, like in the JHipsterProperties above
#
# More documentation is available at:
# https://www.jhipster.tech/common-application-properties/
# ===================================================================

application:
  notification:
    email:
      enabled: true
      from: ${jhipster.mail.from}
    sms:
      enabled: true
      default-provider: TENCENT
      access-key-id: 'your-aliyun-access-key-id'
      access-key-secret: 'your-aliyun-access-key-secret'
      sign-name: 'WhiskerGuard'
      tencent:
        enabled: true
        secret-id: 'AKID0JUSCj3EBuSnwugFjCVlz61lbN4cAcvE'
        secret-key: 'MVTEk3A3iAOSIafYDesPmvrZ4L2XptkN'
        app-id: '**********'
        sign-name: '猫伯伯'
        region: 'ap-nanjing'
    push:
      enabled: false
      app-key: 'your-jpush-app-key'
      master-secret: 'your-jpush-master-secret'
      production: false
#短信验证码
  verification-code:
    enabled: true # 启用验证码服务
    code-length: 4 # 验证码长度
    expiration-minutes: 5 # 验证码有效期（分钟）
    send-interval-seconds: 60 # 发送间隔（秒）

  electronic-signature:
    callback-base-url: 'http://api.whiskerguard.com/api'
    fadada:
      enabled: true
      app-id: 'your-fadada-app-id'
      app-secret: 'your-fadada-app-secret'
      api-url: 'https://api.fadada.com/api/v2'
      callback-url: '/signature/callback/fadada'
    esign:
      enabled: true
      app-id: 'your-esign-app-id'
      app-secret: 'your-esign-app-secret'
      api-url: 'https://openapi.esign.cn'
      callback-url: '/signature/callback/esign'

cos:
  tencent:
    secretId: 'AKIDKXmtpZ5Qb9VfbJp7A7tRmwvgIlCTYP7f'
    secretKey: 'PaaPemlew1sW8aoqfHfqnbCZidLp57xa'
    region: 'ap-nanjing'
    bucketName: 'maobobo-1346546874'
    domain: 'dev.static.mbbhg.com'

logging:
  file:
    name: logs/whiskerguard-general-service.log

sensitive:
  reload-cron: '0 0/5 * * * ?' # 每 5 分钟热更新词库
  default-mask-char: '*'

spring:
  # ...existing code...
  cache:
    caffeine:
      spec: maximumSize=50,expireAfterWrite=30s
  servlet:
    multipart:
      max-file-size: 1024MB
      max-request-size: 1024MB
server:
  undertow:
    max-http-post-size: 1073741824 # 1G
